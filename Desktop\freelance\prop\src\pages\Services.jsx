import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import {
  HiDesktopComputer,
  HiGlobeAlt,
  HiSpeakerphone,
  HiShieldCheck,
  HiSupport,
  HiCreditCard,
  HiLightningBolt,
  HiChartBar,
  HiOfficeBuilding,
  HiCog,
  HiUsers,
  HiArrowRight
} from 'react-icons/hi'

const Services = () => {
  const services = [
    {
      icon: HiDesktopComputer,
      title: 'Dashboard & Tech',
      description: 'Comprehensive dashboard and technology solutions for managing your prop firm operations efficiently.',
      features: ['Real-time analytics', 'Risk management tools', 'Trader management', 'Automated reporting'],
      color: 'bg-blue-500',
      slug: 'dashboard-tech'
    },
    {
      icon: HiGlobeAlt,
      title: 'Web Development',
      description: 'Professional website development and branding services to establish your online presence.',
      features: ['Custom website design', 'Mobile responsive', 'SEO optimization', 'Brand identity'],
      color: 'bg-purple-500',
      slug: 'web-development'
    },
    {
      icon: HiSpeakerphone,
      title: 'Marketing & PR',
      description: 'Strategic marketing and public relations services to grow your prop firm\'s reach and reputation.',
      features: ['Digital marketing', 'Content creation', 'Social media management', 'PR campaigns'],
      color: 'bg-red-500',
      slug: 'marketing-pr'
    },
    {
      icon: HiShieldCheck,
      title: 'Reputation Management',
      description: 'Protect and enhance your prop firm\'s reputation with our comprehensive management services.',
      features: ['Online reputation monitoring', 'Crisis management', 'Review management', 'Brand protection'],
      color: 'bg-green-500',
      slug: 'reputation-management'
    },
    {
      icon: HiSupport,
      title: '24/7 Customer Support',
      description: 'Round-the-clock multilingual customer support to ensure your traders get help when they need it.',
      features: ['24/7 availability', 'Multilingual support', 'Live chat', 'Phone support'],
      color: 'bg-indigo-500',
      slug: 'customer-support'
    },
    {
      icon: HiCreditCard,
      title: 'Payment Solutions',
      description: 'Integrated payment processing solutions supporting multiple payment methods and currencies.',
      features: ['Multiple payment methods', 'Cryptocurrency support', 'Global processing', 'Secure transactions'],
      color: 'bg-orange-500',
      slug: 'payment-solutions'
    },
    {
      icon: HiLightningBolt,
      title: 'Consulting Services',
      description: 'Expert consulting services to help you navigate the complexities of running a prop firm.',
      features: ['Business strategy', 'Regulatory compliance', 'Market analysis', 'Growth planning'],
      color: 'bg-yellow-500',
      slug: 'consulting-services'
    },
    {
      icon: HiChartBar,
      title: 'Trading Platforms',
      description: 'Access to multiple professional trading platforms to meet your traders\' diverse needs.',
      features: ['Multiple platforms', 'Easy integration', 'Custom configurations', 'Technical support'],
      color: 'bg-pink-500',
      slug: 'trading-platforms'
    },
    {
      icon: HiOfficeBuilding,
      title: 'Broker & Liquidity Solutions',
      description: 'Connect with top-tier brokers and liquidity providers for optimal trading conditions.',
      features: ['Broker partnerships', 'Liquidity solutions', 'Competitive spreads', 'Reliable execution'],
      color: 'bg-teal-500',
      slug: 'broker-liquidity'
    },
    {
      icon: HiCog,
      title: 'Risk Management Solutions',
      description: 'Advanced risk management tools and strategies to protect your prop firm and traders.',
      features: ['Risk monitoring', 'Automated controls', 'Compliance tools', 'Reporting systems'],
      color: 'bg-gray-500',
      slug: 'risk-management'
    },
    {
      icon: HiUsers,
      title: 'HR & Recruitment',
      description: 'Human resources and recruitment services to help you build and manage your trading team.',
      features: ['Trader recruitment', 'HR management', 'Performance tracking', 'Team building'],
      color: 'bg-cyan-500',
      slug: 'hr-recruitment'
    }
  ]

  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="section-padding gradient-bg text-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              Our Services
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 mb-8">
              Comprehensive solutions to launch, manage, and scale your prop trading firm with confidence.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Complete Prop Firm Solutions
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From technology infrastructure to marketing and support, we provide everything you need to succeed in the prop trading industry.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => {
              const IconComponent = service.icon
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group"
                >
                  <Link to={`/services/${service.slug}`}>
                    <div className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 p-8 h-full card-hover border border-gray-100">
                      <div className={`${service.color} w-16 h-16 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      
                      <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-primary-600 transition-colors">
                        {service.title}
                      </h3>
                      
                      <p className="text-gray-600 mb-6 leading-relaxed">
                        {service.description}
                      </p>
                      
                      <ul className="space-y-2 mb-6">
                        {service.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center gap-2 text-sm text-gray-600">
                            <div className="w-1.5 h-1.5 bg-primary-600 rounded-full"></div>
                            {feature}
                          </li>
                        ))}
                      </ul>
                      
                      <div className="flex items-center text-primary-600 font-semibold group-hover:gap-3 transition-all duration-300">
                        <span>Learn More</span>
                        <HiArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                      </div>
                    </div>
                  </Link>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <div className="bg-gradient-to-r from-primary-50 to-purple-50 rounded-2xl p-8 md:p-12">
              <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                Ready to Launch Your Prop Firm?
              </h3>
              <p className="text-gray-600 text-lg mb-8 max-w-2xl mx-auto">
                Get started with our comprehensive prop firm solutions and launch your business in just 10 days. Our team is ready to help you succeed.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link 
                  to="/get-in-touch" 
                  className="btn-primary text-lg px-8 py-4"
                >
                  Get Started Today
                  <HiArrowRight className="w-5 h-5" />
                </Link>
                <Link 
                  to="/about-us" 
                  className="btn-secondary text-lg px-8 py-4"
                >
                  Learn More About Us
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

export default Services
