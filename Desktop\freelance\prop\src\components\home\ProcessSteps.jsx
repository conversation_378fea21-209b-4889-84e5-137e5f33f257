import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  HiChat,
  HiCog,
  HiSupport,
  HiCheckCircle,
  HiClock,
  HiUsers,
  HiTrendingUp
} from 'react-icons/hi'
import { Card } from '../ui/Card'
import But<PERSON> from '../ui/Button'

const ProcessSteps = () => {
  const [activeStep, setActiveStep] = useState(0)

  const steps = [
    {
      id: 1,
      title: 'Discovery & Consultation',
      subtitle: 'Understanding Your Vision',
      description: 'We start with a comprehensive consultation to understand your business goals, target market, and specific requirements. Our experts analyze your needs and create a customized roadmap for your prop firm.',
      icon: HiChat,
      color: 'bg-blue-500',
      gradient: 'from-blue-500 to-blue-600',
      duration: '1-2 Days',
      deliverables: [
        'Business requirements analysis',
        'Technical architecture planning',
        'Custom solution design',
        'Project timeline and milestones'
      ],
      features: [
        'Free consultation call',
        'Detailed needs assessment',
        'Competitive analysis',
        'Technology recommendations'
      ]
    },
    {
      id: 2,
      title: 'Design & Development',
      subtitle: 'Building Your Platform',
      description: 'Our development team creates your custom prop firm platform with cutting-edge technology. We build everything from the dashboard to trader interfaces, ensuring scalability and security.',
      icon: HiCog,
      color: 'bg-purple-500',
      gradient: 'from-purple-500 to-purple-600',
      duration: '3-5 Days',
      deliverables: [
        'Custom dashboard development',
        'Trader portal creation',
        'Risk management system',
        'Payment integration setup'
      ],
      features: [
        'Modern, responsive design',
        'Advanced security measures',
        'Scalable architecture',
        'API integrations'
      ]
    },
    {
      id: 3,
      title: 'Testing & Integration',
      subtitle: 'Ensuring Everything Works',
      description: 'Rigorous testing ensures your platform performs flawlessly. We integrate all systems, conduct security audits, and perform load testing to guarantee reliability and performance.',
      icon: HiCheckCircle,
      color: 'bg-green-500',
      gradient: 'from-green-500 to-green-600',
      duration: '2-3 Days',
      deliverables: [
        'Comprehensive system testing',
        'Security audit and compliance',
        'Performance optimization',
        'Integration verification'
      ],
      features: [
        'Automated testing suites',
        'Security penetration testing',
        'Load and stress testing',
        'Cross-platform compatibility'
      ]
    },
    {
      id: 4,
      title: 'Launch & Go-Live',
      subtitle: 'Your Prop Firm is Ready',
      description: 'We handle the complete launch process, from domain setup to trader onboarding. Your prop firm goes live with full support and monitoring to ensure a smooth start.',
      icon: HiTrendingUp,
      color: 'bg-orange-500',
      gradient: 'from-orange-500 to-orange-600',
      duration: '1 Day',
      deliverables: [
        'Production deployment',
        'Domain and SSL setup',
        'Monitoring configuration',
        'Launch day support'
      ],
      features: [
        'Zero-downtime deployment',
        'Real-time monitoring',
        'Immediate support',
        'Performance tracking'
      ]
    },
    {
      id: 5,
      title: 'Ongoing Support',
      subtitle: 'Continuous Growth',
      description: 'Our partnership doesn\'t end at launch. We provide 24/7 support, regular updates, and help you scale your operations as your prop firm grows.',
      icon: HiSupport,
      color: 'bg-indigo-500',
      gradient: 'from-indigo-500 to-indigo-600',
      duration: 'Ongoing',
      deliverables: [
        '24/7 technical support',
        'Regular platform updates',
        'Performance monitoring',
        'Growth consultation'
      ],
      features: [
        'Dedicated support team',
        'Regular health checks',
        'Feature enhancements',
        'Scaling assistance'
      ]
    }
  ]

  const totalDuration = '7-10 Days'

  return (
    <section className="py-16 lg:py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Launch Your Prop Firm in {totalDuration}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Our proven 5-step process ensures a smooth, efficient launch of your prop trading firm with minimal hassle and maximum results.
          </p>
          
          {/* Timeline Overview */}
          <div className="bg-gradient-to-r from-primary-50 to-accent-50 rounded-2xl p-6 max-w-4xl mx-auto">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <HiClock className="w-8 h-8 text-primary-600 mx-auto mb-2" />
                <div className="font-bold text-gray-900">Total Time</div>
                <div className="text-primary-600">{totalDuration}</div>
              </div>
              <div>
                <HiUsers className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <div className="font-bold text-gray-900">Dedicated Team</div>
                <div className="text-green-600">5+ Experts</div>
              </div>
              <div>
                <HiTrendingUp className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                <div className="font-bold text-gray-900">Success Rate</div>
                <div className="text-purple-600">100%</div>
              </div>
              <div>
                <HiSupport className="w-8 h-8 text-orange-600 mx-auto mb-2" />
                <div className="font-bold text-gray-900">Support</div>
                <div className="text-orange-600">24/7</div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Process Steps */}
        <div className="relative">
          {/* Progress Line */}
          <div className="hidden lg:block absolute left-1/2 transform -translate-x-1/2 w-1 bg-gray-200 h-full">
            <motion.div
              className="w-full bg-gradient-to-b from-primary-500 to-accent-500"
              initial={{ height: 0 }}
              whileInView={{ height: `${((activeStep + 1) / steps.length) * 100}%` }}
              transition={{ duration: 1, delay: 0.5 }}
              viewport={{ once: true }}
            />
          </div>

          <div className="space-y-12">
            {steps.map((step, index) => {
              const IconComponent = step.icon
              const isActive = index <= activeStep
              const isEven = index % 2 === 0

              return (
                <motion.div
                  key={step.id}
                  initial={{ opacity: 0, x: isEven ? -50 : 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                  viewport={{ once: true }}
                  onViewportEnter={() => setActiveStep(index)}
                  className={`relative ${isEven ? 'lg:pr-1/2' : 'lg:pl-1/2 lg:ml-auto'}`}
                >
                  {/* Step Number Circle */}
                  <div className="hidden lg:block absolute top-8 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <motion.div
                      className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-white ${
                        isActive ? step.color : 'bg-gray-300'
                      }`}
                      animate={{ scale: isActive ? 1.1 : 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      {step.id}
                    </motion.div>
                  </div>

                  <Card 
                    className={`${isEven ? 'lg:mr-12' : 'lg:ml-12'} overflow-hidden`}
                    hover={true}
                  >
                    <div className="p-8">
                      {/* Mobile Step Number */}
                      <div className="lg:hidden flex items-center mb-6">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-white mr-4 ${step.color}`}>
                          {step.id}
                        </div>
                        <div className="text-sm text-gray-500 font-medium">
                          Step {step.id} • {step.duration}
                        </div>
                      </div>

                      <div className="flex items-start gap-6">
                        <div className={`${step.color} w-16 h-16 rounded-xl flex items-center justify-center flex-shrink-0`}>
                          <IconComponent className="w-8 h-8 text-white" />
                        </div>
                        
                        <div className="flex-1">
                          <div className="hidden lg:block text-sm text-gray-500 font-medium mb-2">
                            Step {step.id} • {step.duration}
                          </div>
                          <h3 className="text-2xl font-bold text-gray-900 mb-2">
                            {step.title}
                          </h3>
                          <h4 className="text-lg text-primary-600 font-semibold mb-4">
                            {step.subtitle}
                          </h4>
                          <p className="text-gray-600 mb-6 leading-relaxed">
                            {step.description}
                          </p>

                          <div className="grid md:grid-cols-2 gap-6">
                            {/* Deliverables */}
                            <div>
                              <h5 className="font-semibold text-gray-900 mb-3">Key Deliverables</h5>
                              <ul className="space-y-2">
                                {step.deliverables.map((deliverable, idx) => (
                                  <li key={idx} className="flex items-start gap-2 text-sm text-gray-600">
                                    <HiCheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                                    {deliverable}
                                  </li>
                                ))}
                              </ul>
                            </div>

                            {/* Features */}
                            <div>
                              <h5 className="font-semibold text-gray-900 mb-3">Key Features</h5>
                              <ul className="space-y-2">
                                {step.features.map((feature, idx) => (
                                  <li key={idx} className="flex items-start gap-2 text-sm text-gray-600">
                                    <HiCheckCircle className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                                    {feature}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              )
            })}
          </div>
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-primary-600 to-accent-600 rounded-2xl p-8 md:p-12 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Ready to Start Your Journey?
            </h3>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Join 50+ successful prop firms who have launched with YourPropFirm. 
              Let's discuss your vision and create a customized plan for your success.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                variant="secondary"
                size="lg"
                className="bg-white text-primary-600 hover:bg-gray-100"
              >
                Schedule Free Consultation
              </Button>
              <Button 
                variant="outline"
                size="lg"
                className="border-white text-white hover:bg-white hover:text-primary-600"
              >
                View Pricing Plans
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default ProcessSteps
